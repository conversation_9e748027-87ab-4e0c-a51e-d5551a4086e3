{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:werewolf", "is_spawnable": true, "is_summonable": true, "properties": {"ditsh:fog_active": {"type": "bool", "default": false}}}, "component_groups": {"ditsh:fog_active_group": {"minecraft:timer": {"time": 1.0, "looping": true, "time_down_event": {"event": "ditsh:check_for_players"}}}, "ditsh:active": {"minecraft:looked_at": {"search_radius": 128.0, "look_at_locations": [{"location": "head"}, {"location": "body"}, {"location": "feet", "vertical_offset": 0.5}], "set_target": "never", "find_players_only": true, "looked_at_cooldown": 0.1, "field_of_view": 5, "scale_fov_by_distance": false, "line_of_sight_obstruction_type": "collision_for_camera", "looked_at_event": {"event": "ditsh:teleport_and_kill", "target": "self"}, "filters": {"all_of": [{"test": "actor_health", "subject": "other", "operator": ">", "value": 0}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_game_mode", "subject": "other", "operator": "!=", "value": "creative"}, {"test": "is_game_mode", "subject": "other", "operator": "!=", "value": "spectator"}]}}}}, "components": {"minecraft:type_family": {"family": ["monster", "werewolf", "ditsh", "mob"]}, "minecraft:collision_box": {"width": 0.8, "height": 2.9}, "minecraft:health": {"value": 200, "max": 200}, "minecraft:attack": {"damage": 20}, "minecraft:movement": {"value": 0.0}, "minecraft:navigation.walk": {"can_path_over_water": false, "avoid_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}, "minecraft:persistent": {}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ditsh:fog_active_group", "ditsh:active"]}, "set_property": {"ditsh:fog_active": true}, "trigger": {"event": "ditsh:start_fog_interval"}}, "ditsh:start_fog_interval": {"trigger": {"event": "ditsh:start_fog_interval", "target": "self"}}, "ditsh:check_for_players": {"trigger": {"event": "ditsh:check_for_players", "target": "self"}}, "ditsh:teleport_and_kill": {"trigger": {"event": "ditsh:teleport_and_kill", "target": "self"}}, "ditsh:on_death": {"trigger": {"event": "ditsh:on_death", "target": "self"}}, "minecraft:entity_born": {"add": {"component_groups": ["ditsh:fog_active_group", "ditsh:active"]}, "set_property": {"ditsh:fog_active": true}, "trigger": {"event": "ditsh:start_fog_interval"}}}}}