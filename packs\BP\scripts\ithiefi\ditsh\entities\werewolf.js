import { GameMode, system, EntityDamageCause, world } from "@minecraft/server";
import { getDistance } from "../utilities/distance";
import { fixedLenRaycast } from "../utilities/raycast";
const WEREWOLF_DAMAGE = 20;
const DETECTION_RADIUS = 128;
const RAYCAST_RADIUS = 5;
const RAYCAST_STEP = 2;
const WEREWOLF_FOG_ID = "ditsh:werewolf_fog";
const activeWerewolfEntities = new Map();
const playersWithWerewolfFog = new Set();
function isPlayerLookingAtWerewolf(player, werewolfLocation) {
    try {
        const playerLocation = {
            x: player.location.x,
            y: player.location.y + 1.6,
            z: player.location.z
        };
        const viewDirection = player.getViewDirection();
        const maxDistance = getDistance(playerLocation, werewolfLocation);
        const raycastStep = RAYCAST_STEP;
        const detectionRadius = RAYCAST_RADIUS;
        const raycastPoints = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);
        for (const rayPoint of raycastPoints) {
            const distanceToWerewolf = getDistance(rayPoint, werewolfLocation);
            if (distanceToWerewolf <= detectionRadius) {
                if (rayPoint.y >= werewolfLocation.y && rayPoint.y <= werewolfLocation.y + 3.0) {
                    return true;
                }
            }
        }
        return false;
    }
    catch (error) {
        return false;
    }
}
function applyWerewolfFog(player) {
    try {
        const playerId = player.id;
        if (!playersWithWerewolfFog.has(playerId)) {
            player.runCommand(`fog @s push ${WEREWOLF_FOG_ID}`);
            playersWithWerewolfFog.add(playerId);
        }
    }
    catch (error) {
        console.warn(`Failed to apply werewolf fog to player: ${error}`);
    }
}
function clearWerewolfFog(player) {
    try {
        const playerId = player.id;
        if (playersWithWerewolfFog.has(playerId)) {
            player.runCommand(`fog @s clear`);
            playersWithWerewolfFog.delete(playerId);
        }
    }
    catch (error) {
        console.warn(`Failed to clear werewolf fog from player: ${error}`);
    }
}
function clearWerewolfFogFromAllPlayers() {
    try {
        const allPlayers = [...playersWithWerewolfFog].map(playerId => {
            try {
                for (const dimension of [world.getDimension("overworld"), world.getDimension("nether"), world.getDimension("the_end")]) {
                    const players = dimension.getPlayers();
                    const player = players.find(p => p.id === playerId);
                    if (player)
                        return player;
                }
                return null;
            }
            catch {
                return null;
            }
        }).filter(player => player !== null);
        for (const player of allPlayers) {
            clearWerewolfFog(player);
        }
    }
    catch (error) {
        console.warn(`Failed to clear werewolf fog from all players: ${error}`);
    }
}
function werewolfFogHandler(werewolf) {
    try {
        const werewolfLocation = werewolf.location;
        const players = werewolf.dimension.getPlayers({
            location: werewolfLocation,
            maxDistance: DETECTION_RADIUS,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        for (const player of players) {
            applyWerewolfFog(player);
        }
    }
    catch (error) {
        console.warn(`Failed to handle werewolf fog: ${error}`);
    }
}
export function startWerewolfFogInterval(werewolf) {
    try {
        const werewolfId = werewolf.id;
        if (activeWerewolfEntities.has(werewolfId)) {
            return;
        }
        const intervalId = system.runInterval(() => {
            if (!werewolf.isValid()) {
                stopWerewolfFogInterval(werewolf);
                return;
            }
            werewolfFogHandler(werewolf);
        }, 20);
        activeWerewolfEntities.set(werewolfId, intervalId);
    }
    catch (error) {
        console.warn(`Failed to start werewolf fog interval: ${error}`);
    }
}
export function stopWerewolfFogInterval(werewolf) {
    try {
        const werewolfId = werewolf.id;
        const intervalId = activeWerewolfEntities.get(werewolfId);
        if (intervalId !== undefined) {
            system.clearRun(intervalId);
            activeWerewolfEntities.delete(werewolfId);
        }
        clearWerewolfFogFromAllPlayers();
    }
    catch (error) {
        console.warn(`Failed to stop werewolf fog interval: ${error}`);
    }
}
export function werewolfTeleportAndKill(werewolf) {
    try {
        const werewolfLocation = werewolf.location;
        const players = werewolf.dimension.getPlayers({
            location: werewolfLocation,
            maxDistance: DETECTION_RADIUS,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        for (const player of players) {
            if (isPlayerLookingAtWerewolf(player, werewolfLocation)) {
                werewolf.teleport(player.location);
                player.applyDamage(WEREWOLF_DAMAGE, {
                    cause: EntityDamageCause.entityAttack,
                    damagingEntity: werewolf
                });
                werewolf.dimension.playSound("mob.ditsh.werewolf.kill", player.location);
                break;
            }
        }
    }
    catch (error) {
        console.warn(`Failed to handle werewolf teleport and kill: ${error}`);
    }
}
export function clearWerewolfFogOnPlayerJoin(player) {
    clearWerewolfFog(player);
}
export function cleanupWerewolfTracking() {
    for (const [werewolfId, intervalId] of activeWerewolfEntities) {
    }
}
