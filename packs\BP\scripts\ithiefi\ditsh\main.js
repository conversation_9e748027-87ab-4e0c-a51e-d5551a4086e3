import { world, system, GameMode } from "@minecraft/server";
import { initEntityListeners } from "./entities/index";
function initWerewolfFogSystem() {
    const WEREWOLF_FOG_ID = "ditsh:werewolf_fog";
    const DETECTION_RADIUS = 128;
    system.runInterval(() => {
        try {
            const allPlayers = [
                ...world.getDimension("overworld").getPlayers(),
                ...world.getDimension("nether").getPlayers(),
                ...world.getDimension("the_end").getPlayers()
            ].filter(player => player.getGameMode() !== GameMode.Creative &&
                player.getGameMode() !== GameMode.Spectator);
            for (const player of allPlayers) {
                try {
                    const nearbyWerewolves = player.dimension.getEntities({
                        type: "ditsh:werewolf",
                        location: player.location,
                        maxDistance: DETECTION_RADIUS
                    });
                    const hasNearbyWerewolf = nearbyWerewolves.length > 0;
                    const currentlyHasFog = player.getDynamicProperty("ditsh:werewolf_fog") === true;
                    if (hasNearbyWerewolf && !currentlyHasFog) {
                        player.runCommand(`fog @s push ${WEREWOLF_FOG_ID}`);
                        player.setDynamicProperty("ditsh:werewolf_fog", true);
                    }
                    else if (!hasNearbyWerewolf && currentlyHasFog) {
                        player.runCommand(`fog @s clear`);
                        player.setDynamicProperty("ditsh:werewolf_fog", false);
                    }
                }
                catch (error) {
                    console.warn(`Failed to process werewolf fog for player: ${error}`);
                }
            }
        }
        catch (error) {
            console.warn(`Failed to run werewolf fog system: ${error}`);
        }
    }, 20);
}
function initPlayerFogCleanup() {
    world.afterEvents.playerSpawn.subscribe((event) => {
        try {
            const player = event.player;
            player.runCommand(`fog @s clear`);
            player.setDynamicProperty("ditsh:werewolf_fog", false);
        }
        catch (error) {
            console.warn(`Failed to clear werewolf fog on player spawn: ${error}`);
        }
    });
}
initEntityListeners();
initWerewolfFogSystem();
initPlayerFogCleanup();
