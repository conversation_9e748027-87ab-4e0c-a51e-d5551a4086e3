import { Enti<PERSON>, Player, Vector3, GameMode, system, EntityDamageCause, world } from "@minecraft/server";
import { getDistance } from "../utilities/distance";
import { fixedLenRaycast } from "../utilities/raycast";

/**
 * @fileoverview Werewolf Entity Handler for DitSH Add-On
 * 
 * This module handles the werewolf entity behavior including:
 * - Fog application to nearby players
 * - Teleportation and damage to players looking at it
 * - Player detection and line-of-sight checking
 * - Fog cleanup on death and player respawn
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

/** Damage dealt to players when werewolf teleports to them (10 hearts = 20 damage) */
const WEREWOLF_DAMAGE: number = 20;

/** Detection radius for players (128 blocks) */
const DETECTION_RADIUS: number = 128;

/** Raycast parameters for line-of-sight detection */
const RAYCAST_RADIUS: number = 5;
const RAYCAST_STEP: number = 2;

/** Fog identifier for werewolf red mist effect */
const WEREWOLF_FOG_ID: string = "ditsh:werewolf_fog";

/** Map to track active werewolf entities and their fog intervals */
const activeWerewolfEntities = new Map<string, number>();

/** Map to track players with werewolf fog applied */
const playersWithWerewolfFog = new Set<string>();

/**
 * Checks if a player is looking at the werewolf using fixed length raycast.
 * Uses 2-block step size and 5-block radius detection per raycast point.
 * 
 * @param player - The player to check
 * @param werewolfLocation - The werewolf's current location
 * @returns True if the player is looking at the werewolf
 */
function isPlayerLookingAtWerewolf(player: Player, werewolfLocation: Vector3): boolean {
  try {
    const playerLocation: Vector3 = {
      x: player.location.x,
      y: player.location.y + 1.6, // Eye level
      z: player.location.z
    };

    const viewDirection: Vector3 = player.getViewDirection();
    const maxDistance: number = getDistance(playerLocation, werewolfLocation);
    const raycastStep: number = RAYCAST_STEP;
    const detectionRadius: number = RAYCAST_RADIUS;

    // Perform fixed length raycast from player's head location
    const raycastPoints: Vector3[] = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);

    // Check each raycast point for proximity to werewolf
    for (const rayPoint of raycastPoints) {
      const distanceToWerewolf: number = getDistance(rayPoint, werewolfLocation);
      
      if (distanceToWerewolf <= detectionRadius) {
        // Check if the raycast point is within the vertical detection area (3 blocks high)
        if (rayPoint.y >= werewolfLocation.y && rayPoint.y <= werewolfLocation.y + 3.0) {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Applies werewolf fog to a player if not already applied.
 * 
 * @param player - The player to apply fog to
 */
function applyWerewolfFog(player: Player): void {
  try {
    const playerId: string = player.id;
    
    if (!playersWithWerewolfFog.has(playerId)) {
      player.runCommand(`fog @s push ${WEREWOLF_FOG_ID}`);
      playersWithWerewolfFog.add(playerId);
    }
  } catch (error) {
    console.warn(`Failed to apply werewolf fog to player: ${error}`);
  }
}

/**
 * Clears werewolf fog from a player.
 * 
 * @param player - The player to clear fog from
 */
function clearWerewolfFog(player: Player): void {
  try {
    const playerId: string = player.id;
    
    if (playersWithWerewolfFog.has(playerId)) {
      player.runCommand(`fog @s clear`);
      playersWithWerewolfFog.delete(playerId);
    }
  } catch (error) {
    console.warn(`Failed to clear werewolf fog from player: ${error}`);
  }
}

/**
 * Clears werewolf fog from all players who currently have it applied.
 */
function clearWerewolfFogFromAllPlayers(): void {
  try {
    // Get all players in the world
    const allPlayers: Player[] = [...playersWithWerewolfFog].map(playerId => {
      try {
        // Try to get player by ID from all dimensions
        for (const dimension of [world.getDimension("overworld"), world.getDimension("nether"), world.getDimension("the_end")]) {
          const players = dimension.getPlayers();
          const player = players.find(p => p.id === playerId);
          if (player) return player;
        }
        return null;
      } catch {
        return null;
      }
    }).filter(player => player !== null) as Player[];

    // Clear fog from all players
    for (const player of allPlayers) {
      clearWerewolfFog(player);
    }
  } catch (error) {
    console.warn(`Failed to clear werewolf fog from all players: ${error}`);
  }
}

/**
 * Handles the periodic fog application for a werewolf entity.
 * Checks for players within 128 blocks every second and applies fog.
 * 
 * @param werewolf - The werewolf entity
 */
function werewolfFogHandler(werewolf: Entity): void {
  try {
    const werewolfLocation: Vector3 = werewolf.location;

    // Get all valid players within 128 blocks
    const players: Player[] = werewolf.dimension.getPlayers({
      location: werewolfLocation,
      maxDistance: DETECTION_RADIUS,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    // Apply fog to all nearby players
    for (const player of players) {
      applyWerewolfFog(player);
    }
  } catch (error) {
    console.warn(`Failed to handle werewolf fog: ${error}`);
  }
}

/**
 * Starts the fog interval for a werewolf entity.
 * Triggers fog application every 1 second (20 ticks).
 * 
 * @param werewolf - The werewolf entity to start fog for
 */
export function startWerewolfFogInterval(werewolf: Entity): void {
  try {
    const werewolfId: string = werewolf.id;
    
    // Don't start if already running
    if (activeWerewolfEntities.has(werewolfId)) {
      return;
    }

    // Start interval for fog application every 1 second
    const intervalId: number = system.runInterval(() => {
      // Check if werewolf still exists
      if (!werewolf.isValid()) {
        stopWerewolfFogInterval(werewolf);
        return;
      }
      
      werewolfFogHandler(werewolf);
    }, 20); // 20 ticks = 1 second

    activeWerewolfEntities.set(werewolfId, intervalId);
  } catch (error) {
    console.warn(`Failed to start werewolf fog interval: ${error}`);
  }
}

/**
 * Stops the fog interval for a werewolf entity and clears fog from all players.
 * 
 * @param werewolf - The werewolf entity to stop fog for
 */
export function stopWerewolfFogInterval(werewolf: Entity): void {
  try {
    const werewolfId: string = werewolf.id;
    const intervalId: number | undefined = activeWerewolfEntities.get(werewolfId);
    
    if (intervalId !== undefined) {
      system.clearRun(intervalId);
      activeWerewolfEntities.delete(werewolfId);
    }

    // Clear fog from all players when werewolf is killed/removed
    clearWerewolfFogFromAllPlayers();
  } catch (error) {
    console.warn(`Failed to stop werewolf fog interval: ${error}`);
  }
}

/**
 * Handles werewolf teleportation to a player who is looking at it.
 * Teleports directly to the player and immediately deals 10 hearts of damage.
 * 
 * @param werewolf - The werewolf entity
 */
export function werewolfTeleportAndKill(werewolf: Entity): void {
  try {
    const werewolfLocation: Vector3 = werewolf.location;

    // Get all valid players within detection radius
    const players: Player[] = werewolf.dimension.getPlayers({
      location: werewolfLocation,
      maxDistance: DETECTION_RADIUS,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    // Find the first player looking at the werewolf
    for (const player of players) {
      if (isPlayerLookingAtWerewolf(player, werewolfLocation)) {
        // Teleport werewolf directly to the player
        werewolf.teleport(player.location);

        // Immediately damage the player for 10 hearts
        player.applyDamage(WEREWOLF_DAMAGE, {
          cause: EntityDamageCause.entityAttack,
          damagingEntity: werewolf
        });

        // Play kill sound effect
        werewolf.dimension.playSound("mob.ditsh.werewolf.kill", player.location);

        // Only teleport to the first player found looking
        break;
      }
    }
  } catch (error) {
    console.warn(`Failed to handle werewolf teleport and kill: ${error}`);
  }
}

/**
 * Clears werewolf fog from a player when they respawn or join the world.
 * This function should be called from the main entity load event handler.
 * 
 * @param player - The player to clear werewolf fog from
 */
export function clearWerewolfFogOnPlayerJoin(player: Player): void {
  clearWerewolfFog(player);
}

/**
 * Cleanup function to remove tracking for invalid werewolf entities.
 * Should be called periodically to prevent memory leaks.
 */
export function cleanupWerewolfTracking(): void {
  for (const [werewolfId, intervalId] of activeWerewolfEntities) {
    // Note: We can't easily check if entity is valid without reference
    // This cleanup will be handled when intervals detect invalid entities
  }
}
